# 更新日志

## [2.0.0] - 2024-12-19

### 🚀 重大更新：公开服务模式

#### ✨ 新增功能

- **🆓 公开服务**：移除管理员密码限制，任何人都可以创建代理
- **🔐 独立管理**：每个代理生成独立的管理密码
- **🎛️ 双模式界面**：
  - 创建新代理模式
  - 管理现有代理模式（需要代理ID + 密码）
- **📊 完整管理界面**：
  - 查看代理详细信息
  - 显示白名单IP列表
  - 添加IP到白名单
  - 标签页式管理界面
- **🎨 美观UI设计**：
  - 模式切换按钮
  - 标签页导航
  - 响应式布局
  - 现代化视觉效果

#### 🔄 重大变更

- **路由更新**：
  - `/admin` 功能迁移到 `/`（首页）
  - 保持 `/admin` 路由兼容性（重定向到首页）
- **数据结构**：
  - 新增 `pwd:代理ID` 存储管理密码
  - 保持现有 `sub:代理ID` 和 `whitelist:代理ID` 结构
- **API响应**：
  - 创建代理时返回管理密码
  - 管理接口需要密码验证

#### 🛠️ 技术改进

- **JavaScript重构**：
  - 模式切换功能
  - 标签页管理
  - 表单处理优化
- **CSS增强**：
  - 新增模式切换样式
  - 标签页样式
  - 改进的响应式设计
- **错误处理**：
  - 更友好的错误提示
  - 详细的状态反馈

#### 📝 文档更新

- **README.md**：更新为公开服务说明
- **部署指南**：新增 DEPLOYMENT.md
- **配置文件**：移除 ADMIN_PASSWORD 环境变量

#### ⚠️ 破坏性变更

- **环境变量**：不再需要 `ADMIN_PASSWORD`
- **管理方式**：从集中管理改为分散管理
- **访问控制**：从管理员控制改为用户自主控制

#### 🔒 安全考虑

- 每个代理的管理密码独立生成
- 管理密码无法恢复，需要用户妥善保存
- 白名单仍受密码保护，确保安全性

---

## [1.0.0] - 2024-12-18

### 🎉 初始版本

#### ✨ 核心功能

- **🔐 管理员模式**：需要管理员密码访问
- **🌐 订阅代理**：将原始订阅地址转换为代理地址
- **🔒 IP白名单**：只有白名单中的IP才能访问代理
- **🎨 状态检查UI**：美观的白名单状态查看界面
- **📱 响应式设计**：支持移动设备访问

#### 🛠️ 技术特性

- 基于 Cloudflare Workers
- 使用 Cloudflare KV 存储
- 现代化 Web 界面
- RESTful API 设计

#### 📊 功能模块

- **管理界面**：`/admin`
- **API接口**：`/api/whitelist/{proxy_id}`
- **代理服务**：`/proxy/{proxy_id}`
- **状态检查**：`/status/{proxy_id}`
- **白名单助手**：`/whitelist-helper`

---

## 版本说明

### 版本号规则

采用语义化版本控制（Semantic Versioning）：
- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 升级指南

#### 从 1.x 升级到 2.0

1. **备份数据**：
   ```bash
   wrangler kv:key list --binding=SUBSCRIPTION_KV > backup.json
   ```

2. **更新配置**：
   - 移除 `wrangler.toml` 中的 `ADMIN_PASSWORD`
   - 部署新版本代码

3. **数据兼容性**：
   - 现有代理继续工作
   - 现有白名单保持有效
   - 需要为现有代理生成管理密码（如需管理）

4. **用户通知**：
   - 通知用户新的管理方式
   - 提供新的管理界面链接
   - 说明管理密码的重要性

### 未来计划

- **2.1.0**：批量管理功能
- **2.2.0**：使用统计和分析
- **2.3.0**：API密钥管理
- **3.0.0**：多租户支持

---

**注意**：升级前请务必备份数据，并在测试环境中验证新功能。
