# 公开订阅代理服务部署指南

## 🚀 快速部署

### 1. 准备工作

确保您已安装：
- [Node.js](https://nodejs.org/) (推荐 LTS 版本)
- [Wrangler CLI](https://developers.cloudflare.com/workers/wrangler/install-and-update/)

```bash
npm install -g wrangler
```

### 2. 登录 Cloudflare

```bash
wrangler auth login
```

### 3. 创建 KV 命名空间

```bash
# 创建生产环境 KV 命名空间
wrangler kv:namespace create "SUBSCRIPTION_KV"

# 创建预览环境 KV 命名空间（可选）
wrangler kv:namespace create "SUBSCRIPTION_KV" --preview
```

### 4. 配置 wrangler.toml

将生成的 KV 命名空间 ID 复制到 `wrangler.toml` 文件中：

```toml
name = "cf-subscription-proxy"
main = "src/index.js"
compatibility_date = "2024-01-01"

# KV namespace for storing subscription URLs and IP whitelist
[[kv_namespaces]]
binding = "SUBSCRIPTION_KV"
id = "你的KV命名空间ID"
preview_id = "你的预览KV命名空间ID"  # 可选

# 公开服务无需环境变量
```

### 5. 部署到 Cloudflare Workers

```bash
# 开发模式（本地测试）
wrangler dev

# 部署到生产环境
wrangler deploy
```

## 🔧 配置说明

### 服务模式

这是一个**公开服务**，特点：
- ✅ 无需管理员密码
- ✅ 任何人都可以创建代理
- ✅ 每个代理有独立的管理密码
- ✅ 支持自助管理白名单

### 安全考虑

1. **资源使用**：由于是公开服务，建议监控资源使用情况
2. **存储限制**：Cloudflare KV 有存储限制，注意监控使用量
3. **访问控制**：虽然是公开服务，但每个代理的白名单仍受独立密码保护

## 📊 监控和维护

### 查看使用情况

```bash
# 查看 Workers 使用情况
wrangler metrics

# 查看 KV 存储使用情况
wrangler kv:key list --binding=SUBSCRIPTION_KV
```

### 备份数据

```bash
# 导出所有 KV 数据
wrangler kv:key list --binding=SUBSCRIPTION_KV > kv_backup.json
```

### 清理数据

如需清理过期或无用的代理数据：

```bash
# 删除特定代理的数据
wrangler kv:key delete "sub:代理ID" --binding=SUBSCRIPTION_KV
wrangler kv:key delete "pwd:代理ID" --binding=SUBSCRIPTION_KV
wrangler kv:key delete "whitelist:代理ID" --binding=SUBSCRIPTION_KV
```

## 🌐 自定义域名（可选）

### 1. 添加自定义域名

在 Cloudflare Dashboard 中：
1. 进入 Workers & Pages
2. 选择您的 Worker
3. 点击 "Settings" > "Triggers"
4. 添加自定义域名

### 2. 更新 DNS 记录

确保您的域名指向 Cloudflare Workers：
```
your-domain.com CNAME your-worker.your-subdomain.workers.dev
```

## 🔍 故障排除

### 常见问题

1. **KV 命名空间错误**
   ```
   Error: KV namespace not found
   ```
   解决：检查 `wrangler.toml` 中的 KV 命名空间 ID 是否正确

2. **部署失败**
   ```
   Error: Authentication failed
   ```
   解决：重新运行 `wrangler auth login`

3. **代理访问失败**
   - 检查原始订阅地址是否可访问
   - 确认 IP 是否在白名单中
   - 查看 Workers 日志：`wrangler tail`

### 查看日志

```bash
# 实时查看 Workers 日志
wrangler tail

# 查看特定时间段的日志
wrangler tail --since 1h
```

## 📈 性能优化

### 1. 缓存策略

考虑为订阅内容添加适当的缓存：
- 静态内容：长期缓存
- 订阅内容：短期缓存（避免频繁请求源站）

### 2. 错误处理

确保所有 API 调用都有适当的错误处理和重试机制。

### 3. 监控指标

定期检查：
- 请求量和响应时间
- 错误率
- KV 存储使用量
- Workers 执行时间

## 🔄 更新服务

### 更新代码

```bash
# 拉取最新代码
git pull origin main

# 重新部署
wrangler deploy
```

### 数据迁移

如果需要迁移现有数据，请先备份，然后使用脚本批量处理。

## 📞 支持

如遇到问题：
1. 查看 [Cloudflare Workers 文档](https://developers.cloudflare.com/workers/)
2. 检查项目的 GitHub Issues
3. 查看 Cloudflare Dashboard 中的错误日志

---

**注意**：这是一个公开服务，请确保合理使用资源，并定期监控服务状态。
